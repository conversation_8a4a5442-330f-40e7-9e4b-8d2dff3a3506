defmodule Drops.OperationsTest do
  use Drops.OperationCase, async: true

  describe "basic operations" do
    operation :command do
      @impl true
      def perform(params) do
        if params[:name] == nil do
          {:error, "name is required"}
        else
          {:ok, params}
        end
      end
    end

    test "it works without schema", %{operation: operation} do
      {:ok, %{result: result, params: params}} = operation.execute(%{name: "<PERSON>"})

      assert result == %{name: "<PERSON>"}
      assert params == %{name: "<PERSON>"}
    end
  end

  describe "operations with schema" do
    operation :command do
      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def perform(params) do
        if params[:name] != "<PERSON> Doe" do
          {:error, "name is not expected"}
        else
          {:ok, params}
        end
      end
    end

    test "it works with a schema", %{operation: operation} do
      {:ok, %{result: result, params: params}} =
        operation.execute(%{name: "<PERSON>"})

      assert result == %{name: "<PERSON>"}
      assert params == %{name: "<PERSON>"}

      {:error, %{result: result, params: params}} =
        operation.execute(%{name: ""})

      assert_errors(["name must be filled"], {:error, result})
      assert params == %{name: ""}
    end
  end

  describe "operations with Ecto schema" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :command, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    test "it works with an Ecto schema", %{operation: operation} do
      {:ok, %{result: result, type: :command, params: params}} =
        operation.execute(%{name: "Jane Doe", email: "<EMAIL>"})

      assert result == %{name: "Jane Doe"}
      assert params == %{name: "Jane Doe", email: "<EMAIL>"}
    end
  end

  describe "operations with custom validation" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :command, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end

      def validate(changeset) do
        changeset |> validate_required([:email])
      end
    end

    test "it works with an Ecto schema and custom validation logic", %{
      operation: operation
    } do
      {:ok, %{result: result, type: :command, params: params}} =
        operation.execute(%{
          name: "Jane Doe",
          email: "<EMAIL>"
        })

      assert result == %{name: "Jane Doe"}
      assert params == %{name: "Jane Doe", email: "<EMAIL>"}

      {:error, %{result: changeset, type: :command, params: _params}} =
        operation.execute(%{name: "Jane Doe", email: ""})

      # Verify that the changeset has validation errors for email
      assert %Ecto.Changeset{} = changeset
      refute changeset.valid?
      assert changeset.errors[:email]
    end
  end

  describe "operations with accept option" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :command, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema, accept: [:name])

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name, email: user.email}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    test "it works with an Ecto schema and :accept option", %{operation: operation} do
      {:ok, %{result: result, type: :command, params: params}} =
        operation.execute(%{
          name: "Jane Doe",
          email: "<EMAIL>"
        })

      assert result == %{name: "Jane Doe", email: nil}
      assert params == %{name: "Jane Doe"}
    end
  end

  describe "operations with embedded schemas" do
    @tag ecto_schemas: [Test.Ecto.UserWithAddressSchema]
    operation type: :command, repo: Drops.TestRepo do
      schema(Test.Ecto.UserWithAddressSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name, address: user.address}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    test "it works with an Ecto schema with embedded fields", %{operation: operation} do
      # Test with valid embedded data
      valid_params = %{
        name: "John Doe",
        email: "<EMAIL>",
        address: %{
          street: "123 Main St",
          city: "Anytown",
          state: "CA",
          zip_code: "12345",
          country: "USA"
        }
      }

      {:ok, %{result: result, type: :command, params: params}} =
        operation.execute(valid_params)

      assert result.name == "John Doe"
      assert result.address.street == "123 Main St"
      assert result.address.city == "Anytown"
      assert params.name == "John Doe"
      assert params.address.street == "123 Main St"

      # Test with extra fields that should be filtered out
      params_with_extra = %{
        name: "Jane Smith",
        email: "<EMAIL>",
        address: %{
          street: "456 Oak Ave",
          city: "Springfield",
          state: "IL",
          zip_code: "62701"
        },
        age: 28,
        extra_field: "this should be ignored"
      }

      {:ok, %{result: result, params: params}} =
        operation.execute(params_with_extra)

      assert result.name == "Jane Smith"
      assert result.address.street == "456 Oak Ave"
      assert result.address.city == "Springfield"
      # Extra fields should be filtered out
      refute Map.has_key?(params, :age)
      refute Map.has_key?(params, :extra_field)
    end
  end

  describe ":form commands" do
    @describetag ecto_schemas: [Test.Ecto.UserSchema]

    operation type: :form, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        case persist(params) do
          {:ok, user} -> {:ok, %{name: user.name}}
          {:error, changeset} -> {:error, changeset}
        end
      end
    end

    test "it works with an Ecto schema", %{operation: operation} do
      {:ok, %{result: result, type: :form, params: params}} =
        operation.execute(%{
          "name" => "Jane Doe",
          "email" => "<EMAIL>"
        })

      assert result == %{name: "Jane Doe"}
      assert params == %{name: "Jane Doe", email: "<EMAIL>"}
    end

    test "Success struct implements Phoenix.HTML.FormData protocol", %{
      operation: operation
    } do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          {:ok, success} =
            operation.execute(%{
              "name" => "Jane Doe",
              "email" => "<EMAIL>"
            })

          # Test that Success struct can be converted to a form
          form = Phoenix.HTML.FormData.to_form(success, [])
          assert is_struct(form)
          assert form.data == %{"name" => "Jane Doe", "email" => "<EMAIL>"}

          # Test input_value function
          assert Phoenix.HTML.FormData.input_value(success, form, :name) == "Jane Doe"

          assert Phoenix.HTML.FormData.input_value(success, form, :email) ==
                   "<EMAIL>"

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end
  end

  describe ":form commands - failure cases" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :form, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(_params) do
        {:error, "Something went wrong"}
      end
    end

    test "Failure struct implements Phoenix.HTML.FormData protocol", %{
      operation: operation
    } do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          {:error, failure} =
            operation.execute(%{
              "name" => "Jane Doe",
              "email" => "<EMAIL>"
            })

          # Test that Failure struct can be converted to a form
          form = Phoenix.HTML.FormData.to_form(failure, [])
          assert is_struct(form)
          assert form.data == %{"name" => "Jane Doe", "email" => "<EMAIL>"}

          # Test input_value function
          assert Phoenix.HTML.FormData.input_value(failure, form, :name) == "Jane Doe"

          assert Phoenix.HTML.FormData.input_value(failure, form, :email) ==
                   "<EMAIL>"

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end
  end

  describe ":form commands - changeset failures" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :form, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        # Create an invalid changeset to test error handling
        changeset =
          params
          |> changeset()
          |> Ecto.Changeset.add_error(:name, "is required")
          |> Map.put(:action, :validate)

        {:error, changeset}
      end
    end

    test "Failure struct with Ecto.Changeset implements Phoenix.HTML.FormData protocol",
         %{
           operation: operation
         } do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          {:error, failure} =
            operation.execute(%{
              "name" => "",
              "email" => "<EMAIL>"
            })

          # Test that Failure struct with changeset can be converted to a form
          form = Phoenix.HTML.FormData.to_form(failure, [])
          assert is_struct(form)

          # The form should use the changeset data
          assert %Ecto.Changeset{} = form.source

          # Test that errors are preserved
          assert form.errors[:name] == {"is required", []}

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end
  end

  describe ":form commands - schema validation" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :form, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema)

      @impl true
      def perform(params) do
        {:ok, params}
      end
    end

    test "Form command with schema validation errors converts to changeset for FormData",
         %{
           operation: operation
         } do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          # Test with invalid data that fails schema validation (using string keys like real forms)
          # Missing "email" field will cause schema validation to fail
          {:error, failure} =
            operation.execute(%{
              "name" => "John Doe"
            })

          # Test that Failure struct with schema errors can be converted to a form
          form = Phoenix.HTML.FormData.to_form(failure, [])
          assert is_struct(form)

          # The form should use a changeset with converted errors
          assert %Ecto.Changeset{} = form.source
          assert form.errors[:email] == {"key must be present", []}

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end
  end

  describe ":form commands - changeset validation" do
    @tag ecto_schemas: [Test.Ecto.UserSchema]
    operation type: :form, repo: Drops.TestRepo do
      schema(Test.Ecto.UserSchema, default_presence: :optional)

      @impl true
      def perform(params) do
        {:ok, params}
      end

      def validate(changeset) do
        changeset |> validate_required([:name])
      end
    end

    test "Form command with changeset validation errors", %{operation: operation} do
      # Only run this test if Phoenix.HTML is available
      case Code.ensure_loaded(Phoenix.HTML.FormData) do
        {:module, _} ->
          # Test with data that passes schema validation but fails changeset validation (using string keys like real forms)
          # Omit name field to pass schema validation but fail changeset validation
          {:error, failure} =
            operation.execute(%{
              "email" => "<EMAIL>"
            })

          # Test that Failure struct with changeset errors can be converted to a form
          form = Phoenix.HTML.FormData.to_form(failure, [])
          assert is_struct(form)

          # The form should use the changeset with validation errors
          assert %Ecto.Changeset{} = form.source
          assert form.errors[:name] == {"can't be blank", [validation: :required]}

        {:error, _} ->
          # Skip test if Phoenix.HTML is not available
          :ok
      end
    end
  end

  describe "using prepare/1" do
    operation :command do
      schema do
        %{
          required(:name) => string(:filled?),
          required(:template) => boolean()
        }
      end

      @impl true
      def prepare(%{template: true} = params) do
        Map.put(params, :name, params.name <> ".template")
      end

      @impl true
      def perform(params) do
        {:ok, params}
      end
    end

    test "passes prepared params to perform", %{operation: operation} do
      {:ok, %{result: result, params: params}} =
        operation.execute(%{name: "README.md", template: true})

      assert result == %{name: "README.md.template", template: true}
      assert params == %{name: "README.md.template", template: true}
    end
  end
end
